#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP组播发送端
发送组播消息到指定的组播地址和端口
"""

import socket
import time
import json
from datetime import datetime

class UDPMulticastSender:
    def __init__(self, multicast_group='*********', port=5007, ttl=2):
        """
        初始化UDP组播发送端
        
        Args:
            multicast_group (str): 组播地址，默认为 *********
            port (int): 端口号，默认为 5007
            ttl (int): TTL值，默认为 2
        """
        self.multicast_group = multicast_group
        self.port = port
        self.ttl = ttl
        self.sock = None
        
    def setup_socket(self):
        """设置UDP套接字"""
        try:
            # 创建UDP套接字
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            
            # 设置TTL值
            self.sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, self.ttl)
            
            # 允许地址重用
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            print(f"UDP组播发送端已设置完成")
            print(f"组播地址: {self.multicast_group}")
            print(f"端口: {self.port}")
            print(f"TTL: {self.ttl}")
            
        except Exception as e:
            print(f"设置套接字失败: {e}")
            raise
    
    def send_message(self, message):
        """
        发送单条消息
        
        Args:
            message (str): 要发送的消息
        """
        try:
            if isinstance(message, str):
                message = message.encode('utf-8')
            
            self.sock.sendto(message, (self.multicast_group, self.port))
            print(f"已发送消息: {message.decode('utf-8')}")
            
        except Exception as e:
            print(f"发送消息失败: {e}")
    
    def send_json_message(self, data):
        """
        发送JSON格式的消息
        
        Args:
            data (dict): 要发送的数据字典
        """
        try:
            json_message = json.dumps(data, ensure_ascii=False)
            self.send_message(json_message)
            
        except Exception as e:
            print(f"发送JSON消息失败: {e}")
    
    def start_periodic_sending(self, interval=2):
        """
        开始周期性发送消息
        
        Args:
            interval (int): 发送间隔（秒）
        """
        print(f"开始每{interval}秒发送一次消息，按Ctrl+C停止...")
        
        counter = 0
        try:
            while True:
                counter += 1
                
                # 发送简单文本消息
                simple_message = f"Hello Multicast #{counter}"
                self.send_message(simple_message)
                
                # 发送JSON格式消息
                json_data = {
                    "序号": counter,
                    "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "发送者": "UDP组播发送端",
                    "消息": f"这是第{counter}条组播消息"
                }
                self.send_json_message(json_data)
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n收到停止信号，正在关闭发送端...")
        except Exception as e:
            print(f"发送过程中出错: {e}")
    
    def close(self):
        """关闭套接字"""
        if self.sock:
            self.sock.close()
            print("UDP组播发送端已关闭")

def main():
    """主函数"""
    print("=== UDP组播发送端 ===")
    
    # 创建发送端实例
    sender = UDPMulticastSender()
    
    try:
        # 设置套接字
        sender.setup_socket()
        
        # 发送几条测试消息
        print("\n发送测试消息...")
        sender.send_message("测试消息1: Hello UDP Multicast!")
        sender.send_json_message({
            "类型": "测试",
            "内容": "这是一条JSON格式的测试消息",
            "时间": datetime.now().isoformat()
        })
        
        # 开始周期性发送
        print("\n开始周期性发送...")
        sender.start_periodic_sending(interval=3)
        
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        sender.close()

if __name__ == "__main__":
    main()
