#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UDP组播收发函数
"""

import socket
import time

def send_multicast(message, multicast_group='*********', port=5007):
    """
    发送UDP组播消息

    Args:
        message (str): 要发送的消息
        multicast_group (str): 组播地址，默认为 *********
        port (int): 端口号，默认为 5007
    """
    # 创建UDP套接字
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    # 设置TTL值
    sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, 2)

    try:
        # 发送消息
        sock.sendto(message.encode('utf-8'), (multicast_group, port))
        print(f"已发送消息到 {multicast_group}:{port}: {message}")
    finally:
        sock.close()

def send_multicast_loop(multicast_group='*********', port=5007, interval=2):
    """
    循环发送UDP组播消息

    Args:
        multicast_group (str): 组播地址，默认为 *********
        port (int): 端口号，默认为 5007
        interval (int): 发送间隔（秒）
    """
    print(f"开始每{interval}秒发送一次消息到 {multicast_group}:{port}")
    print("按Ctrl+C停止...")

    counter = 0
    try:
        while True:
            counter += 1
            message = f"Hello Multicast #{counter}"
            send_multicast(message, multicast_group, port)
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\n停止发送")

if __name__ == "__main__":
    send_multicast('你好，udp')
    # print("=== UDP组播发送端 ===")
    # print("1. 发送单条消息")
    # print("2. 循环发送消息")

    # choice = input("请选择 (1/2): ")

    # if choice == '1':
    #     message = input("请输入要发送的消息: ")
    #     send_multicast(message)
    # elif choice == '2':
    #     send_multicast_loop()
    # else:
    #     print("无效选择")
