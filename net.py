import socket
import netifaces

def show_network_interfaces():
    """显示所有网络接口"""
    for interface in netifaces.interfaces():
        addrs = netifaces.ifaddresses(interface)
        if netifaces.AF_INET in addrs:
            for addr in addrs[netifaces.AF_INET]:
                print(f"网卡: {interface}, IP: {addr['addr']}")

show_network_interfaces()
# 或者更简单的方法
hostname = socket.gethostname()
local_ip = socket.gethostbyname(hostname)
print(f"主机名: {hostname}")
print(f"主IP: {local_ip}")