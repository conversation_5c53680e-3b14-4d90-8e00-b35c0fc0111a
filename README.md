# 简单的UDP组播工具

这是一个简单的UDP组播收发工具，包含两个基本函数：发送和接收。

## 文件说明

- `udp_multicast_sender.py` - 包含发送函数
- `udp_multicast_receiver.py` - 包含接收函数
- `test_multicast.py` - 简单测试脚本
- `README.md` - 说明文档

## 快速开始

### 方法1：使用测试脚本（最简单）

```bash
python test_multicast.py
```

选择选项3可以同时测试发送和接收。

### 方法2：分别运行

#### 接收消息
```bash
python udp_multicast_receiver.py
```
选择1开始接收消息

#### 发送消息
```bash
python udp_multicast_sender.py
```
选择1发送单条消息，或选择2循环发送

## 在代码中使用

### 发送消息
```python
from udp_multicast_sender import send_multicast

# 发送一条消息
send_multicast("Hello World!")

# 发送到指定地址和端口
send_multicast("Hello", multicast_group='224.2.2.2', port=8888)
```

### 接收消息
```python
from udp_multicast_receiver import receive_multicast

# 开始接收消息（会一直运行直到按Ctrl+C）
receive_multicast()

# 接收指定地址和端口的消息
receive_multicast(multicast_group='224.2.2.2', port=8888)
```

## 默认设置

- 组播地址：224.1.1.1
- 端口：5007
- TTL：2

## 注意事项

1. 确保防火墙允许UDP流量
2. 在同一网络内测试
3. 如果收不到消息，尝试以管理员身份运行

## 简单测试步骤

1. 打开两个终端窗口
2. 在第一个窗口运行：`python udp_multicast_receiver.py`，选择1
3. 在第二个窗口运行：`python udp_multicast_sender.py`，选择1或2
4. 在接收端应该能看到发送的消息
