# UDP组播测试工具

这个项目包含了UDP组播的发送端和接收端实现，可以在本地进行组播通信测试。

## 文件说明

- `udp_multicast_sender.py` - UDP组播发送端
- `udp_multicast_receiver.py` - UDP组播接收端  
- `test_multicast.py` - 测试脚本
- `README.md` - 说明文档

## 功能特性

### 发送端功能
- 支持发送文本消息和JSON格式消息
- 可配置组播地址、端口和TTL值
- 支持周期性发送消息
- 支持单次发送和批量发送

### 接收端功能
- 自动解析文本和JSON格式消息
- 显示发送方信息和接收时间
- 支持单次接收和持续监听
- 支持超时设置

## 使用方法

### 方法1：使用测试脚本（推荐）

运行测试脚本，选择相应的测试模式：

```bash
python test_multicast.py
```

选择选项3进行并发测试，可以同时测试发送和接收功能。

### 方法2：分别运行发送端和接收端

#### 启动接收端
在第一个终端窗口中运行：
```bash
python udp_multicast_receiver.py
```

#### 启动发送端
在第二个终端窗口中运行：
```bash
python udp_multicast_sender.py
```

### 方法3：在代码中使用

#### 发送端示例
```python
from udp_multicast_sender import UDPMulticastSender

# 创建发送端
sender = UDPMulticastSender(multicast_group='224.1.1.1', port=5007)
sender.setup_socket()

# 发送消息
sender.send_message("Hello Multicast!")
sender.send_json_message({"type": "test", "data": "example"})

# 关闭
sender.close()
```

#### 接收端示例
```python
from udp_multicast_receiver import UDPMulticastReceiver

# 创建接收端
receiver = UDPMulticastReceiver(multicast_group='224.1.1.1', port=5007)
receiver.setup_socket()

# 接收单条消息
message, addr = receiver.receive_single_message(timeout=5)
if message:
    print(f"收到消息: {message['原始文本']}")

# 关闭
receiver.close()
```

## 配置参数

### 默认配置
- 组播地址：224.1.1.1
- 端口：5007
- TTL：2

### 自定义配置
可以在创建实例时修改这些参数：

```python
# 自定义组播地址和端口
sender = UDPMulticastSender(multicast_group='224.2.2.2', port=8888, ttl=3)
receiver = UDPMulticastReceiver(multicast_group='224.2.2.2', port=8888)
```

## 注意事项

1. **防火墙设置**：确保防火墙允许UDP流量通过指定端口
2. **组播地址范围**：使用224.0.0.0到239.255.255.255范围内的地址
3. **网络环境**：在某些网络环境中，组播可能被路由器或交换机阻止
4. **权限要求**：在某些系统上可能需要管理员权限来绑定端口

## 故障排除

### 常见问题

1. **接收不到消息**
   - 检查防火墙设置
   - 确认组播地址和端口配置一致
   - 尝试使用不同的组播地址

2. **权限错误**
   - 在Windows上以管理员身份运行
   - 在Linux/Mac上使用sudo运行

3. **网络问题**
   - 确保在同一网络段内
   - 检查路由器是否支持组播

### 测试连通性

可以使用以下命令测试网络组播功能：

```bash
# 在Linux/Mac上测试组播
ping 224.1.1.1
```

## 扩展功能

这些脚本可以作为基础，扩展以下功能：
- 加密通信
- 消息确认机制
- 负载均衡
- 消息队列
- 日志记录
- 性能监控
