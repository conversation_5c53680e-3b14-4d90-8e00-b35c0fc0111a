#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UDP组播测试
"""

import threading
import time
from udp_multicast_sender import send_multicast
from udp_multicast_receiver import receive_multicast

def test_send():
    """测试发送几条消息"""
    print("发送测试消息...")
    for i in range(3):
        send_multicast(f"测试消息 #{i+1}")
        time.sleep(1)
    print("发送完成")

def test_concurrent():
    """同时测试发送和接收"""
    print("=== 同时测试发送和接收 ===")

    # 启动接收线程
    receiver_thread = threading.Thread(target=receive_multicast)
    receiver_thread.daemon = True  # 设为守护线程
    receiver_thread.start()

    # 等待1秒让接收端启动
    time.sleep(1)

    # 发送测试消息
    test_send()

if __name__ == "__main__":
    print("=== UDP组播简单测试 ===")
    print("1. 只发送消息")
    print("2. 只接收消息")
    print("3. 同时测试（推荐）")

    choice = input("请选择 (1/2/3): ")

    if choice == '1':
        test_send()
    elif choice == '2':
        receive_multicast()
    elif choice == '3':
        test_concurrent()
    else:
        print("无效选择")
