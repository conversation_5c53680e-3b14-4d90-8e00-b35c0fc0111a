#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP组播测试脚本
提供简单的测试功能来验证组播收发
"""

import threading
import time
import sys
from udp_multicast_sender import UDPMulticastSender
from udp_multicast_receiver import UDPMulticastReceiver

def test_sender():
    """测试发送端"""
    print("启动发送端测试...")
    sender = UDPMulticastSender()
    
    try:
        sender.setup_socket()
        
        # 发送几条测试消息
        for i in range(5):
            sender.send_message(f"测试消息 #{i+1}")
            time.sleep(1)
        
        print("发送端测试完成")
        
    except Exception as e:
        print(f"发送端测试失败: {e}")
    finally:
        sender.close()

def test_receiver():
    """测试接收端"""
    print("启动接收端测试...")
    receiver = UDPMulticastReceiver()
    
    try:
        receiver.setup_socket()
        
        # 接收5条消息后停止
        for i in range(5):
            print(f"等待第{i+1}条消息...")
            message, addr = receiver.receive_single_message(timeout=10)
            if message:
                print(f"收到: {message['原始文本']}")
            else:
                print("超时，未收到消息")
                break
        
        print("接收端测试完成")
        
    except Exception as e:
        print(f"接收端测试失败: {e}")
    finally:
        receiver.close()

def test_concurrent():
    """并发测试：同时启动发送端和接收端"""
    print("=== 并发测试 ===")
    print("同时启动发送端和接收端进行测试...")
    
    # 创建接收端线程
    receiver_thread = threading.Thread(target=test_receiver)
    
    # 启动接收端
    receiver_thread.start()
    
    # 等待1秒确保接收端已启动
    time.sleep(1)
    
    # 启动发送端
    test_sender()
    
    # 等待接收端线程完成
    receiver_thread.join()
    
    print("并发测试完成")

def main():
    """主函数"""
    print("=== UDP组播测试工具 ===")
    print("1. 发送端测试")
    print("2. 接收端测试") 
    print("3. 并发测试（推荐）")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请选择测试类型 (1-4): ").strip()
            
            if choice == '1':
                test_sender()
            elif choice == '2':
                test_receiver()
            elif choice == '3':
                test_concurrent()
            elif choice == '4':
                print("退出测试工具")
                break
            else:
                print("无效选择，请输入1-4")
                
        except KeyboardInterrupt:
            print("\n\n收到中断信号，退出程序")
            break
        except Exception as e:
            print(f"测试过程中出错: {e}")

if __name__ == "__main__":
    main()
