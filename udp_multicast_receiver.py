#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UDP组播收发函数
"""

import socket
import struct

def receive_multicast(multicast_group='*********', port=5007):
    """
    接收UDP组播消息

    Args:
        multicast_group (str): 组播地址，默认为 *********
        port (int): 端口号，默认为 5007
    """
    # 创建UDP套接字
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    # 允许地址重用
    sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

    # 绑定到指定端口
    sock.bind(('', port))

    # 加入组播组
    mreq = struct.pack("4sl", socket.inet_aton(multicast_group), socket.INADDR_ANY)
    sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)

    print(f"开始接收组播消息 {multicast_group}:{port}")
    print("按Ctrl+C停止...")

    try:
        while True:
            # 接收数据
            data, addr = sock.recvfrom(1024)
            message = data.decode('utf-8')
            print(f"收到来自 {addr[0]}:{addr[1]} 的消息: {message}")
    except KeyboardInterrupt:
        print("\n停止接收")
    finally:
        sock.close()

def send_multicast(message, multicast_group='*********', port=5007):
    """
    发送UDP组播消息

    Args:
        message (str): 要发送的消息
        multicast_group (str): 组播地址，默认为 *********
        port (int): 端口号，默认为 5007
    """
    # 创建UDP套接字
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    # 设置TTL值
    sock.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, 2)

    try:
        # 发送消息
        sock.sendto(message.encode('utf-8'), (multicast_group, port))
        print(f"已发送消息到 {multicast_group}:{port}: {message}")
    finally:
        sock.close()

if __name__ == "__main__":
    receive_multicast()
    # print("=== UDP组播测试 ===")
    # print("1. 接收消息")
    # print("2. 发送消息")

    # choice = input("请选择 (1/2): ")

    # if choice == '1':
    #     receive_multicast()
    # elif choice == '2':
    #     message = input("请输入要发送的消息: ")
    #     send_multicast(message)
    # else:
        # print("无效选择")
