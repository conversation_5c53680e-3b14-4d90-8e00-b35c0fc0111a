#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UDP组播接收端
接收指定组播地址和端口的组播消息
"""

import socket
import struct
import json
from datetime import datetime

class UDPMulticastReceiver:
    def __init__(self, multicast_group='*********', port=5007, local_ip=''):
        """
        初始化UDP组播接收端
        
        Args:
            multicast_group (str): 组播地址，默认为 *********
            port (int): 端口号，默认为 5007
            local_ip (str): 本地IP地址，默认为空（自动选择）
        """
        self.multicast_group = multicast_group
        self.port = port
        self.local_ip = local_ip if local_ip else socket.gethostbyname(socket.gethostname())
        self.sock = None
        
    def setup_socket(self):
        """设置UDP套接字"""
        try:
            # 创建UDP套接字
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            
            # 允许地址重用
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 绑定到指定端口
            self.sock.bind(('', self.port))
            
            # 加入组播组
            mreq = struct.pack("4sl", socket.inet_aton(self.multicast_group), socket.INADDR_ANY)
            self.sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)
            
            print(f"UDP组播接收端已设置完成")
            print(f"组播地址: {self.multicast_group}")
            print(f"端口: {self.port}")
            print(f"本地IP: {self.local_ip}")
            print(f"正在监听组播消息...")
            
        except Exception as e:
            print(f"设置套接字失败: {e}")
            raise
    
    def parse_message(self, data):
        """
        解析接收到的消息
        
        Args:
            data (bytes): 接收到的原始数据
            
        Returns:
            dict: 解析后的消息信息
        """
        try:
            # 尝试解码为UTF-8字符串
            message_str = data.decode('utf-8')
            
            # 尝试解析为JSON
            try:
                json_data = json.loads(message_str)
                return {
                    "类型": "JSON",
                    "内容": json_data,
                    "原始文本": message_str
                }
            except json.JSONDecodeError:
                # 不是JSON格式，作为普通文本处理
                return {
                    "类型": "文本",
                    "内容": message_str,
                    "原始文本": message_str
                }
                
        except UnicodeDecodeError:
            # 无法解码为UTF-8，返回原始字节
            return {
                "类型": "二进制",
                "内容": data,
                "原始文本": f"<二进制数据，长度: {len(data)} 字节>"
            }
    
    def start_receiving(self):
        """开始接收组播消息"""
        print("开始接收组播消息，按Ctrl+C停止...")
        print("-" * 60)
        
        message_count = 0
        
        try:
            while True:
                # 接收数据
                data, addr = self.sock.recvfrom(1024)
                message_count += 1
                
                # 解析消息
                parsed_message = self.parse_message(data)
                
                # 显示接收信息
                print(f"\n[消息 #{message_count}] 时间: {datetime.now().strftime('%H:%M:%S')}")
                print(f"发送方: {addr[0]}:{addr[1]}")
                print(f"消息类型: {parsed_message['类型']}")
                
                if parsed_message['类型'] == 'JSON':
                    print("JSON内容:")
                    for key, value in parsed_message['内容'].items():
                        print(f"  {key}: {value}")
                else:
                    print(f"内容: {parsed_message['内容']}")
                
                print("-" * 60)
                
        except KeyboardInterrupt:
            print("\n收到停止信号，正在关闭接收端...")
        except Exception as e:
            print(f"接收过程中出错: {e}")
    
    def receive_single_message(self, timeout=5):
        """
        接收单条消息
        
        Args:
            timeout (int): 超时时间（秒）
            
        Returns:
            tuple: (消息内容, 发送方地址) 或 (None, None) 如果超时
        """
        try:
            self.sock.settimeout(timeout)
            data, addr = self.sock.recvfrom(1024)
            parsed_message = self.parse_message(data)
            return parsed_message, addr
        except socket.timeout:
            print(f"等待{timeout}秒后超时，未收到消息")
            return None, None
        except Exception as e:
            print(f"接收消息失败: {e}")
            return None, None
        finally:
            self.sock.settimeout(None)  # 恢复阻塞模式
    
    def close(self):
        """关闭套接字"""
        if self.sock:
            try:
                # 离开组播组
                mreq = struct.pack("4sl", socket.inet_aton(self.multicast_group), socket.INADDR_ANY)
                self.sock.setsockopt(socket.IPPROTO_IP, socket.IP_DROP_MEMBERSHIP, mreq)
            except:
                pass
            
            self.sock.close()
            print("UDP组播接收端已关闭")

def main():
    """主函数"""
    print("=== UDP组播接收端 ===")
    
    # 创建接收端实例
    receiver = UDPMulticastReceiver()
    
    try:
        # 设置套接字
        receiver.setup_socket()
        
        # 测试接收单条消息
        print("\n测试接收单条消息（5秒超时）...")
        message, addr = receiver.receive_single_message(timeout=5)
        if message:
            print(f"收到来自 {addr} 的消息: {message['原始文本']}")
        
        # 开始持续接收
        print("\n开始持续接收消息...")
        receiver.start_receiving()
        
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        receiver.close()

if __name__ == "__main__":
    main()
